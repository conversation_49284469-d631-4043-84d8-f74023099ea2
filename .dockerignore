# ================================
# Version Control & Git
# ================================
.git
.gitignore

# ================================
# Development & Build Tools
# ================================
Dockerfile
Dockerfile-local
Docker_*.bat
.ignore

# ================================
# Documentation & Examples
# ================================
/docs
README.md

# ================================
# Runtime Data & Logs
# ================================
/data/flows.json
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pids
*.pid
*.seed
*.pid.lock

# ================================
# Dependencies & Cache
# ================================
/node_modules
/jspm_packages
.npm
.eslintcache
*.tgz
.yarn-integrity
typings/

# ================================
# Build & Coverage Output
# ================================
lib-cov
coverage
.nyc_output
build/Release
.next

# ================================
# Development Tools Storage
# ================================
.grunt
bower_components
.lock-wscript

# ================================
# Environment & Config
# ================================
.env
.node_repl_history
