{"name": "node-gw", "description": "A Node-RED Project for bdtd-IoT", "version": "********", "homepage": "http://www.bdtd.com/", "private": true, "__@asinino/node-red-kafkajs": "^0.0.7", "__@flexdash/node-red-fd-corewidgets": "^0.4.64", "__@meowwolf/node-red-contrib-amqp": "^2.4.1", "__@skyswitch/appmetrics": "^5.1.17", "__appmetrics-dash": "^5.3.0", "__node-red-contrib-kafka-manager": "^0.6.2", "__node-red-contrib-ui-led": "^0.4.11", "__node-red-contrib-uibuilder": "^7.0.3", "__node-red-dashboard": "^3.6.5", "__node-red-node-rbe": "^0.5.0", "__node-red-node-tdengine": "^1.0.1", "dependencies": {"@corentin-pasquier/node-red-contrib-amqp": "^1.3.13", "@flowfuse/node-red-dashboard": "^1.24.2", "@hylink/node-red-kafka-client": "^0.2.6", "@serdoo/node-red-contrib-soap": "^0.1.0", "async-mutex": "^0.5.0", "dayjs": "^1.11.10", "node-postgres-named": "^2.4.1", "node-red-configurable-ping": "^1.0.2", "node-red-contrib-advanced-ftp": "^3.0.1", "node-red-contrib-aedes": "^0.13.0", "node-red-contrib-chunks-to-lines": "^0.8.1", "node-red-contrib-dir2files": "^0.3.0", "node-red-contrib-filesystem": "^1.0.0", "node-red-contrib-fs": "^1.4.1", "node-red-contrib-full-msg-json-schema-validation": "^1.1.0", "node-red-contrib-graphql": "^2.1.2", "node-red-contrib-iconv": "0.0.3", "node-red-contrib-iiot-opcua": "^4.1.2", "node-red-contrib-jsonpath": "^0.1.1", "node-red-contrib-lodash-throttle": "^1.0.1", "node-red-contrib-loop-processing": "^0.5.1", "node-red-contrib-md5": "^1.0.4", "node-red-contrib-modbus": "^5.30.0", "node-red-contrib-modbustcp": "^1.2.3", "node-red-contrib-mqtt-broker": "^0.2.9", "node-red-contrib-mssql-plus": "0.12.2", "node-red-contrib-postgresql": "^0.14.0", "node-red-contrib-readdir": "^1.0.1", "node-red-contrib-redis": "^1.3.9", "node-red-contrib-socketio": "^1.1.0", "node-red-contrib-ssh-v3": "~2.0.4", "node-red-node-data-generator": "1.0.2", "node-red-node-mysql": "^2.0.0", "node-red-node-sentiment": "^0.1.6", "node-red-node-snmp": "^2.0.0", "node-red-node-tail": "^0.4.0", "node-red-node-tdengine": "^1.0.1", "number-precision": "^1.6.0", "pg": "^8.11.3", "querystring": "^0.2.0", "readable-stream": "^4.5.2", "superjson": "^2.2.2", "when": "^3.7.8", "xregexp": "^5.1.1"}, "overrides": {"node-gyp": "^9.0.0"}, "engines": {"node": ">=20"}, "volta": {"node": "20.11.1"}}