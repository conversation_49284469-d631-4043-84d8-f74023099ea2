{"name": "node-red-contrib-heapdump", "version": "0.1.0", "description": "A Node Red node to create a heap dump file using Node.js built-in v8 API", "dependencies": {"fs-extra": "^11.3.0"}, "engines": {"node": ">=20"}, "author": {"name": "purezhi"}, "license": "Apache-2.0", "keywords": ["node-red", "heap", "dump", "memory"], "bugs": {"url": "https://github.com/purezhi/node-red-contrib-heapdump/issues"}, "homepage": "https://github.com/purezhi/node-red-contrib-heapdump", "repository": {"type": "git", "url": "https://github.com/purezhi/node-red-contrib-heapdump.git"}, "node-red": {"nodes": {"heap-dump": "heapdump.js"}}}