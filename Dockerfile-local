# FROM node:20-alpine
FROM m.daocloud.io/docker.io/library/node:20-alpine
LABEL maintainer="<PERSON><wang<PERSON><EMAIL>>"

# System update and timezone config
# RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN echo "https://dl-cdn.alpinelinux.org/alpine/edge/community" >> /etc/apk/repositories \
    && echo "https://dl-cdn.alpinelinux.org/alpine/edge/main" >> /etc/apk/repositories
RUN apk update \
  && apk upgrade \
  && apk add --no-cache busybox busybox-extras g++ libc6-compat logrotate make python3=3.11.7-r0 py3-setuptools py3-pip tzdata \
  && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
  && echo "Asia/Shanghai" > /etc/timezone \
  && ln -sf /usr/bin/python3 /usr/bin/python
RUN python3 --version
RUN python3 -c "from distutils.version import StrictVersion; print(StrictVersion('1.2.3'))"

# Setup system logrotation
COPY ./docker/pm2-apps /etc/logrotate.d/
# CMD: /usr/sbin/logrotate -d /etc/logrotate.d/pm2-apps
RUN chmod 644 /etc/logrotate.d/pm2-apps \
  && mkdir -p /var/lib/logrotate \
  && echo "*/1 * * * * /usr/sbin/logrotate /etc/logrotate.d/pm2-apps --state /var/lib/logrotate/status" >> /etc/crontabs/root

# Setup entrypoint
RUN mv /usr/local/bin/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh.bak
COPY ./docker/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Config npm and install PM2 and Logrotation plugin
ENV NPM_CONFIG_LOGLEVEL=warn
RUN npm config set registry https://registry.npmmirror.com \
  && npm install -g --unsafe-perm --no-update-notifier --no-fund --no-audit node-red@4.0.9 \
  && npm install -g --no-update-notifier --no-fund --no-audit pm2

# Bundle APP files
WORKDIR /app
COPY . .

# Install custom nodes
WORKDIR /app/extensions/@bdtd/js-common-polyfill
RUN npm install --omit=dev --legacy-peer-deps
WORKDIR /app/extensions/@purezhi/node-red-contrib-kafkajs
RUN npm install --omit=dev --legacy-peer-deps

# Install normal nodes
WORKDIR /app/profile
RUN cp package-template.json package.json \
  && npm install --omit=dev --legacy-peer-deps --no-update-notifier --no-fund --no-audit \
  && npm cache clean --force

# Expose the listening port of your app
EXPOSE 1880

# Start the app
WORKDIR /app
CMD [ "pm2-runtime", "start", "ecosystem.config.js" ]
